// ignore_for_file: constant_identifier_names

import 'package:e_library/features/auth/register/index.dart';
import 'package:e_library/features/main/index.dart';
import 'package:e_library/features/dashboard/index.dart';
import 'package:e_library/features/books/add_book/index.dart';
import 'package:e_library/features/books/view_books/index.dart';
import 'package:e_library/features/books/search_book/index.dart';
import 'package:e_library/features/authors/add_author/index.dart';
import 'package:e_library/features/authors/search_author/index.dart';
import 'package:e_library/features/authors/author_books/index.dart';
import 'package:e_library/features/add_publisher/index.dart';
import 'package:e_library/features/publishers/search_publisher/index.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../features/auth/login/index.dart';
import '../../features/splash/index.dart';

class AppRouting {
  static GetPage unknownRoute =
      GetPage(name: "/unknown", page: () => SizedBox());

  static GetPage initialRoute = GetPage(
    name: "/",
    page: () => SplashScreen(),
  );

  static List<GetPage> routes = [
    initialRoute,
    ...Pages.values.map((e) => e.page),
  ];
}

enum Pages {
  //Auth
  login,
  register,
  //
  home,
  dashboard,
  addBook,
  addAuthor,
  addPublisher,
  viewBooks,
  searchBook,
  searchAuthor,
  searchPublisher,
  authorBooks,

  //
  ;

  String get value => '/$name';

  GetPage get page => switch (this) {
        login => GetPage(
            name: value,
            page: () => LoginPage(),
          ),
        register => GetPage(
            name: value,
            page: () => RegisterPage(),
          ),
        home => GetPage(
            name: value,
            page: () => MainPage(),
          ),
        dashboard => GetPage(
            name: value,
            page: () => DashboardPage(),
          ),
        addBook => GetPage(
            name: value,
            page: () => AddBookPage(),
          ),
        addAuthor => GetPage(
            name: value,
            page: () => AddAuthorPage(),
          ),
        addPublisher => GetPage(
            name: value,
            page: () => AddPublisherPage(),
          ),
        viewBooks => GetPage(
            name: value,
            page: () => ViewBooksPage(),
          ),
        searchBook => GetPage(
            name: value,
            page: () => SearchBookPage(),
          ),
        searchAuthor => GetPage(
            name: value,
            page: () => SearchAuthorPage(),
          ),
        searchPublisher => GetPage(
            name: value,
            page: () => SearchPublisherPage(),
          ),
        authorBooks => GetPage(
            name: value,
            page: () => AuthorBooksPage(),
          ),
      };
}
