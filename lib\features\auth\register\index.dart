import 'package:e_library/features/auth/register/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RegisterPageController());

    return Form(
      key: controller.formKey,
      child: ListView(
        padding: EdgeInsets.all(15),
        children: [
          SizedBox(height: MediaQuery.sizeOf(context).height * .02),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text("Create_New_Account",
                  style: Theme.of(context).textTheme.headlineSmall),
              SizedBox(
                width: 16,
              ),
            ],
          ),
          Text("Create_New_Account",
              style: Theme.of(context).textTheme.bodyMedium),
          SizedBox(
            height: 35,
          ),
          Text("Create_New_Account",
              style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(height: 6),

          Text("Create_New_Account",
              style: Theme.of(context).textTheme.bodyMedium),
          const Si<PERSON><PERSON><PERSON>(height: 6),
          TextForm<PERSON>ield(
            controller: controller.password,
            decoration: InputDecoration(
              prefixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(width: 8),
                ],
              ),
              hintText: "",
            ),
            // validator: (value) {
            //   if (value!.isEmpty) {
            //     return    Text("Create_New_Account");
            //   }
            // if (!controller.passwordRegex.hasMatch(value)) {
            //   return   Text("Create_New_Account"),;
            // }

            //  return null;
            //     },
          ),
          SizedBox(height: 30),
          Text("Create_New_Account",
              style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(height: 6),
          // TextFormField(
          //   controller: controller.Confirm_Password,
          //   decoration: InputDecoration(
          //     prefixIcon: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         SizedBox(width: 8),
          //         Assets.icons.key.svg(),
          //         SizedBox(width: 8),
          //         Assets.icons.line.svg(),
          //         SizedBox(width: 8),
          //       ],
          //     ),
          //     hintText: tr(LocaleKeys.Confirm_Password),
          //   ),
          //   validator: (value) {
          //     if (controller.password.text != value || value!.isEmpty) {
          //       return tr(LocaleKeys.Passwords_do_not_match);
          //     }

          //     return null;
          //   },
          // ),
          SizedBox(height: 100),
          Center(
            child: ElevatedButton(
              onPressed: controller.confirm,
              child: Text("Create_New_Account"),
            ),
          ),
        ],
      ),
    );
  }
}
