import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/services/state_management/obs.dart';
import 'package:e_library/features/authors/model/author_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class SearchAuthorController extends GetxController {
  AppBuilder appBuilder = Get.find();

  // Search results
  ObsList<AuthorModel> searchResults = ObsList([]);

  // Search functionality
  late TextEditingController searchController;
  RxString searchQuery = ''.obs;

  // Loading state
  RxBool isSearching = false.obs;
  RxBool hasSearched = false.obs;

  // Search filters
  RxString selectedSearchType = 'all'.obs;
  List<String> searchTypes = ['all', 'fname', 'lname', 'country', 'city'];

  @override
  onInit() {
    searchController = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    searchController.dispose();
    super.onClose();
  }

  String getSearchTypeDisplayName(String type) {
    switch (type) {
      case 'all':
        return 'الكل';
      case 'fname':
        return 'الاسم الأول';
      case 'lname':
        return 'الاسم الأخير';
      case 'country':
        return 'البلد';
      case 'city':
        return 'المدينة';
      default:
        return type;
    }
  }

  // Search Authors from API
  searchAuthors() async {
    if (searchController.text.trim().isEmpty) {
      Get.snackbar("تنبيه", "يرجى إدخال كلمة البحث");
      return;
    }

    try {
      isSearching.value = true;
      hasSearched.value = true;
      searchQuery.value = searchController.text.trim();

      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.searchAuthors,
          method: RequestMethod.Get,
          params: {
            "query": searchQuery.value,
            "searchType": selectedSearchType.value,
            "limit": 50,
          },
        ),
      );

      if (response.success) {
        searchResults.value = response.data is List
            ? response.data
            : List<AuthorModel>.from((response.data['authors'] ?? [])
                .map((x) => AuthorModel.fromJson(x)));
      } else {
        searchResults.value = [];
        Get.snackbar("خطأ", "فشل في البحث");
      }
    } catch (e) {
      searchResults.value = [];
      Get.snackbar("خطأ", "فشل في البحث");
    } finally {
      isSearching.value = false;
    }
  }

  // Advanced search with multiple criteria
  advancedSearch({
    String? fname,
    String? lname,
    String? country,
    String? city,
    String? address,
  }) async {
    try {
      isSearching.value = true;
      hasSearched.value = true;

      Map<String, dynamic> searchParams = {};

      if (fname?.isNotEmpty == true) searchParams['fname'] = fname;
      if (lname?.isNotEmpty == true) searchParams['lname'] = lname;
      if (country?.isNotEmpty == true) searchParams['country'] = country;
      if (city?.isNotEmpty == true) searchParams['city'] = city;
      if (address?.isNotEmpty == true) searchParams['address'] = address;

      if (searchParams.isEmpty) {
        Get.snackbar("تنبيه", "يرجى إدخال معايير البحث");
        return;
      }

      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.advancedSearchAuthors,
          method: RequestMethod.Post,
          body: searchParams,
        ),
      );

      if (response.success) {
        searchResults.value = List<Map<String, dynamic>>.from(
            response.data['authors'] ?? response.data);
      } else {
        searchResults.clear();
        Get.snackbar("خطأ", "فشل في البحث المتقدم");
      }
    } catch (e) {
      searchResults.clear();
      Get.snackbar("خطأ", "فشل في البحث المتقدم");
    } finally {
      isSearching.value = false;
    }
  }

  // Clear search
  clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    searchResults.value = [];
    hasSearched.value = false;
    selectedSearchType.value = 'all';
  }

  // Get author's books
  getAuthorBooks(int authorId) async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: "${EndPoints.getAuthors}/$authorId/books",
          method: RequestMethod.Get,
        ),
      );

      if (response.success) {
        return List<Map<String, dynamic>>.from(response.data['books'] ?? []);
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل كتب المؤلف");
    }
    return <Map<String, dynamic>>[];
  }

  // Get search suggestions
  getSearchSuggestions() async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.getAuthorSuggestions,
          method: RequestMethod.Get,
        ),
      );

      if (response.success) {
        return List<String>.from(response.data['suggestions'] ?? []);
      }
    } catch (e) {
      // Handle error silently for suggestions
    }
    return <String>[];
  }
}
