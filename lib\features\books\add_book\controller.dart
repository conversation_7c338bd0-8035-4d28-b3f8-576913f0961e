import 'package:e_library/core/config/app_builder.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class AddBookController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Book fields: Id, Title, Type, Price, pubId, AuthorId
  late TextEditingController title, type, price;

  // Publisher and Author selection from API
  RxString selectedPublisherId = ''.obs;
  RxString selectedAuthorId = ''.obs;

  // Lists from API
  RxList<Map<String, dynamic>> publishers = <Map<String, dynamic>>[].obs;
  RxList<Map<String, dynamic>> authors = <Map<String, dynamic>>[].obs;

  // Loading states
  RxBool isLoadingPublishers = false.obs;
  RxBool isLoadingAuthors = false.obs;

  @override
  onInit() {
    title = TextEditingController();
    type = TextEditingController();
    price = TextEditingController();

    // Load data from API
    loadPublishers();
    loadAuthors();

    super.onInit();
  }

  @override
  onClose() {
    title.dispose();
    type.dispose();
    price.dispose();
    super.onClose();
  }

  // Load Publishers from API
  loadPublishers() async {
    try {
      isLoadingPublishers.value = true;
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.getPublishers,
          method: RequestMethod.Get,
        ),
      );

      if (response.success) {
        publishers.value = List<Map<String, dynamic>>.from(response.data);
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل دور النشر");
    } finally {
      isLoadingPublishers.value = false;
    }
  }

  // Load Authors from API
  loadAuthors() async {
    try {
      isLoadingAuthors.value = true;
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.getAuthors,
          method: RequestMethod.Get,
        ),
      );

      if (response.success) {
        authors.value = List<Map<String, dynamic>>.from(response.data);
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل المؤلفين");
    } finally {
      isLoadingAuthors.value = false;
    }
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedPublisherId.value.isEmpty) {
      Get.snackbar("خطأ", "يرجى اختيار دار النشر");
      return;
    }

    if (selectedAuthorId.value.isEmpty) {
      Get.snackbar("خطأ", "يرجى اختيار المؤلف");
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.addBook,
        method: RequestMethod.Post,
        params: {"success": true},
        body: {
          "title": title.text,
          "type": type.text,
          "price": double.tryParse(price.text) ?? 0.0,
          "pubId": int.tryParse(selectedPublisherId.value) ?? 0,
          "authorId": int.tryParse(selectedAuthorId.value) ?? 0,
        },
      ),
    );

    if (response.success) {
      Get.snackbar(
        "نجح",
        "تم إضافة الكتاب بنجاح",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Clear form
      title.clear();
      type.clear();
      price.clear();
      selectedPublisherId.value = '';
      selectedAuthorId.value = '';
    } else {
      Get.snackbar(
        "خطأ",
        "فشل في إضافة الكتاب",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
