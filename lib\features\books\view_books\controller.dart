import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/features/books/model/book_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class ViewBooksController extends GetxController {
  AppBuilder appBuilder = Get.find();

  // Books list from API
  RxList<BookModel> books = <BookModel>[].obs;
  RxList<BookModel> filteredBooks = <BookModel>[].obs;

  // Search functionality
  late TextEditingController searchController;
  RxString searchQuery = ''.obs;

  // Loading state
  RxBool isLoading = false.obs;

  // Pagination
  RxInt currentPage = 1.obs;
  RxInt totalPages = 1.obs;
  RxBool hasMoreData = true.obs;

  @override
  onInit() {
    searchController = TextEditingController();
    loadBooks();

    // Listen to search changes
    searchController.addListener(() {
      searchQuery.value = searchController.text;
      filterBooks();
    });

    super.onInit();
  }

  @override
  onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Load Books from API
  loadBooks({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        currentPage.value = 1;
        hasMoreData.value = true;
      }

      isLoading.value = true;
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.getBooks,
          method: RequestMethod.Get,
          fromJson: BookModel.fromJson,
          params: {
            "page": currentPage.value,
            "limit": 20,
          },
        ),
      );

      if (response.success) {
        List<BookModel> newBooks = response.data is List
            ? response.data
            : List<BookModel>.from((response.data['books'] ?? [])
                .map((x) => BookModel.fromJson(x)));

        if (isRefresh) {
          books.assignAll(newBooks);
        } else {
          books.addAll(newBooks);
        }

        // Update pagination info
        totalPages.value = response.data['totalPages'] ?? 1;
        hasMoreData.value = currentPage.value < totalPages.value;

        filterBooks();
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل الكتب");
    } finally {
      isLoading.value = false;
    }
  }

  // Load more books (pagination)
  loadMoreBooks() async {
    if (hasMoreData.value && !isLoading.value) {
      currentPage.value++;
      await loadBooks();
    }
  }

  // Filter books based on search query
  filterBooks() {
    if (searchQuery.value.isEmpty) {
      filteredBooks.assignAll(books);
    } else {
      filteredBooks.assignAll(books.where((book) {
        String title = book.title?.toLowerCase() ?? '';
        String type = book.type?.toLowerCase() ?? '';
        String authorName = book.authorName?.toLowerCase() ?? '';
        String publisherName = book.publisherName?.toLowerCase() ?? '';
        String query = searchQuery.value.toLowerCase();

        return title.contains(query) ||
            type.contains(query) ||
            authorName.contains(query) ||
            publisherName.contains(query);
      }).toList());
    }
  }

  // Refresh books
  refreshBooks() async {
    await loadBooks(isRefresh: true);
  }

  // Clear search
  clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    filterBooks();
  }
}
