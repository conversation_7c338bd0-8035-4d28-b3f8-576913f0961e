import 'package:e_library/core/style/repo.dart';
import 'package:e_library/core/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.deepPurple,
              StyleRepo.blue,
              StyleRepo.black.withValues(alpha: 0.7),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 30),
            child: Column(
              children: [
                SizedBox(height: 40),

                // Header
                Text(
                  "مكتبة إلكترونية",
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        blurRadius: 5,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  "إدارة الكتب والمؤلفين ودور النشر",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
                SizedBox(height: 60),

                // Main Actions Grid
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 1,
                    childAspectRatio: 2.5,
                    mainAxisSpacing: 25,
                    children: [
                      // Add Book Card
                      _buildActionCard(
                        title: "إضافة كتاب جديد",
                        subtitle: "أضف كتاباً جديداً إلى المكتبة",
                        icon: Icons.menu_book_rounded,
                        gradientColors: [
                          StyleRepo.deepPurple,
                          StyleRepo.purple,
                        ],
                        onTap: () => Get.toNamed(Pages.addBook.value),
                      ),

                      // Add Author Card
                      _buildActionCard(
                        title: "إضافة مؤلف جديد",
                        subtitle: "أضف مؤلفاً جديداً إلى قاعدة البيانات",
                        icon: Icons.person_add_rounded,
                        gradientColors: [
                          StyleRepo.blue,
                          StyleRepo.bluemedium,
                        ],
                        onTap: () => Get.toNamed(Pages.addAuthor.value),
                      ),

                      // Add Publisher Card
                      _buildActionCard(
                        title: "إضافة دار نشر جديدة",
                        subtitle: "أضف دار نشر جديدة إلى النظام",
                        icon: Icons.business_rounded,
                        gradientColors: [
                          StyleRepo.black,
                          StyleRepo.darkGrey,
                        ],
                        onTap: () => Get.toNamed(Pages.addPublisher.value),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 30),

                // Footer
                Text(
                  "اختر العملية التي تريد القيام بها",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white60,
                  ),
                ),
                SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradientColors,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradientColors,
          ),
          boxShadow: [
            BoxShadow(
              color: gradientColors[0].withValues(alpha: 0.3),
              blurRadius: 15,
              offset: Offset(0, 8),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Row(
            children: [
              // Icon Container
              Container(
                height: 60,
                width: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 20),

              // Text Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),

              // Arrow Icon
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.white70,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
