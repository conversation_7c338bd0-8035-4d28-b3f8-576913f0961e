import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/config/role.dart';
import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/home/<USER>/drawerItem.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller=Get.find< AppBuilder>();
    return Scaffold(
      appBar: AppBar(title: const Text("E_Library")),
      drawer: Drawer(
        child: ListView(
          children: [
            const DrawerHeader(
              child: Text(
                '📚 E_Library',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ),
            // عناصر التنقل داخل التطبيق
            if(controller.role==Role.admin){
              drawerItem('لوحة التحكم', '/dashboard'),
            }
            drawerItem('لوحة التحكم', '/dashboard'),

            drawerItem('تسجيل الدخول', '/login'),

            // drawerItem('View All Books', '/books'),
            // drawerItem('Search Book', '/search-book'),
            // drawerItem('Search Author', '/search-author'),
            // drawerItem('Search Publisher', '/search-publisher'),
          ],
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.lightPurple.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: const Center(
          child: Text(
            "Welcome to your library 📚",
            maxLines: 2,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 20, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
