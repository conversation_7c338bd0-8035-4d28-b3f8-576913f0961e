import 'package:e_library/core/style/repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class AddAuthorPage extends StatelessWidget {
  const AddAuthorPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddAuthorController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.blue,
              StyleRepo.bluemedium,
              StyleRepo.darkPurple,
              StyleRepo.black.withValues(alpha: 0.9),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Form(
            key: controller.formKey,
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 30),
              children: [
                SizedBox(height: 20),
                // Header
                Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon:
                          Icon(Icons.arrow_back, color: Colors.white, size: 28),
                    ),
                    SizedBox(width: 10),
                    Text(
                      "إضافة مؤلف جديد",
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 30),

                // Author Icon
                Center(
                  child: Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Icon(
                        Icons.person_add_rounded,
                        size: 45,
                        color: StyleRepo.blue,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 30),

                // First Name Field
                _buildTextField(
                  controller: controller.fname,
                  hintText: "الاسم الأول",
                  icon: Icons.person,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // Last Name Field
                _buildTextField(
                  controller: controller.lname,
                  hintText: "الاسم الأخير",
                  icon: Icons.person_outline,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // Country Field
                _buildTextField(
                  controller: controller.country,
                  hintText: "البلد",
                  icon: Icons.flag,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // City Field
                _buildTextField(
                  controller: controller.city,
                  hintText: "المدينة",
                  icon: Icons.location_city,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // Address Field
                _buildTextField(
                  controller: controller.address,
                  hintText: "العنوان",
                  icon: Icons.location_on,
                  maxLines: 2,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 40),

                // Add Button
                Container(
                  height: 55,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: StyleRepo.blue.withValues(alpha: 0.4),
                        blurRadius: 10,
                        offset: Offset(0, 5),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: controller.confirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: StyleRepo.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      "إضافة المؤلف",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        decoration: InputDecoration(
          hintText: hintText,
          prefixIcon: Icon(icon, color: StyleRepo.blue),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide(color: StyleRepo.blue, width: 2),
          ),
        ),
        validator: validator,
      ),
    );
  }
}
