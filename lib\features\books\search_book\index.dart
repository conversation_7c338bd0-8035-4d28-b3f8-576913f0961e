import 'package:e_library/core/services/state_management/widgets/obs_widget.dart';
import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/books/model/book_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class SearchBookPage extends StatelessWidget {
  const SearchBookPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SearchBookController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.deepPurple,
              StyleRepo.blue,
              StyleRepo.black.withValues(alpha: 0.8),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon:
                          Icon(Icons.arrow_back, color: Colors.white, size: 28),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        "البحث في الكتب",
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: controller.clearSearch,
                      icon:
                          Icon(Icons.clear_all, color: Colors.white, size: 28),
                    ),
                  ],
                ),
              ),

              // Search Section
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    // Search Type Dropdown
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 8,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Obx(() => DropdownButtonFormField<String>(
                            value: controller.selectedSearchType.value,
                            decoration: InputDecoration(
                              hintText: "نوع البحث",
                              prefixIcon: Icon(Icons.filter_list,
                                  color: StyleRepo.deepPurple),
                              filled: true,
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(25),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(25),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(25),
                                borderSide: BorderSide(
                                    color: StyleRepo.deepPurple, width: 2),
                              ),
                            ),
                            items: controller.searchTypes.map((String type) {
                              return DropdownMenuItem<String>(
                                value: type,
                                child: Text(
                                  controller.getSearchTypeDisplayName(type),
                                  style: TextStyle(
                                    color: StyleRepo.black,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                controller.selectedSearchType.value = newValue;
                              }
                            },
                            dropdownColor: Colors.white,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: StyleRepo.deepPurple,
                            ),
                          )),
                    ),
                    SizedBox(height: 15),

                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 8,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: controller.searchController,
                        onSubmitted: (_) => controller.searchBooks(),
                        decoration: InputDecoration(
                          hintText: "ابحث عن الكتب...",
                          prefixIcon:
                              Icon(Icons.search, color: StyleRepo.deepPurple),
                          suffixIcon: IconButton(
                            onPressed: controller.searchBooks,
                            icon: Icon(Icons.send, color: StyleRepo.deepPurple),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                                color: StyleRepo.deepPurple, width: 2),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 15),

                    // Search Button
                    Container(
                      width: double.infinity,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: StyleRepo.purple.withValues(alpha: 0.4),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: controller.searchBooks,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: StyleRepo.deepPurple,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          elevation: 0,
                        ),
                        child: Obx(() => controller.isSearching.value
                            ? SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                "بحث",
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              )),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),

              // Search Results
              Expanded(
                child: Obx(() {
                  if (!controller.hasSearched.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search,
                            size: 80,
                            color: Colors.white54,
                          ),
                          SizedBox(height: 20),
                          Text(
                            "ابحث عن الكتب",
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white70,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            "استخدم شريط البحث أعلاه للعثور على الكتب",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white54,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.isSearching.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.white),
                          SizedBox(height: 20),
                          Text(
                            "جاري البحث...",
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.searchResults.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 80,
                            color: Colors.white54,
                          ),
                          SizedBox(height: 20),
                          Text(
                            "لا توجد نتائج",
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white70,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'لم يتم العثور على كتب تحتوي على "${controller.searchQuery.value}"',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white54,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    itemCount: controller.searchResults.length,
                    itemBuilder: (context, index) {
                      final book = controller.searchResults[index];
                      return _buildBookCard(book);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookCard(BookModel book) {
    return Container(
      margin: EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(15),
        child: Row(
          children: [
            // Book Icon
            Container(
              height: 60,
              width: 50,
              decoration: BoxDecoration(
                color: StyleRepo.deepPurple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.menu_book,
                color: StyleRepo.deepPurple,
                size: 30,
              ),
            ),
            SizedBox(width: 15),

            // Book Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    book.title ?? 'عنوان غير محدد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: StyleRepo.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 5),
                  Text(
                    'النوع: ${book.type ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 3),
                  Text(
                    'المؤلف: ${book.authorName ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 3),
                  Text(
                    'دار النشر: ${book.publisherName ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // Price
            if (book.price != null)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: StyleRepo.deepPurple,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${book.price} ر.س',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
