import 'package:e_library/core/style/repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SplashScreenController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.royalPurple.withOpacity(0.9),
              StyleRepo.royalPurple.withOpacity(0.7)
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                height: 150,
                width: 150,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    Icons.auto_stories_rounded,
                    size: 80,
                    color: StyleRepo.royalPurple,
                  ),
                ),
              ),
              SizedBox(height: 40),
              // App Name
              Text("Electronic Library",
                  style: Theme.of(context)
                      .textTheme
                      .headlineLarge!
                      .copyWith(color: StyleRepo.blueGray
                          //   shadows: [
                          //   Shadow(
                          //      color: Colors.black26,
                          //     blurRadius: 5,
                          //      offset: Offset(0, 2),
                          //    ),
                          //  ],
                          )),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
