import 'package:e_library/core/config/app_builder.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class AddAuthorController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Author fields: Id, Fname, Lname, Country, City, Address
  late TextEditingController fname, lname, country, city, address;

  @override
  onInit() {
    fname = TextEditingController();
    lname = TextEditingController();
    country = TextEditingController();
    city = TextEditingController();
    address = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    fname.dispose();
    lname.dispose();
    country.dispose();
    city.dispose();
    address.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.addAuthor,
        method: RequestMethod.Post,
        params: {"success": true},
        body: {
          "fname": fname.text,
          "lname": lname.text,
          "country": country.text,
          "city": city.text,
          "address": address.text,
        },
      ),
    );

    if (response.success) {
      Get.snackbar(
        "نجح",
        "تم إضافة المؤلف بنجاح",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Clear form
      fname.clear();
      lname.clear();
      country.clear();
      city.clear();
      address.clear();
    } else {
      Get.snackbar(
        "خطأ",
        "فشل في إضافة المؤلف",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
