import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;

import '../../../core/services/rest_api/rest_api.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController email, password;

  @override
  onInit() {
    email = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    email.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        params: {"success": true},
        body: FormData.fromMap(
          {
            "email": email.text,
            "password": password.text,
          },
        ),
      ),
    );
    if (response.success) {
    } else {}
  }
}
