import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/services/state_management/obs.dart';
import 'package:e_library/features/books/model/book_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class SearchBookController extends GetxController {
  AppBuilder appBuilder = Get.find();

  // Search results
  ObsList<BookModel> searchResults = ObsList([]);

  // Search functionality
  late TextEditingController searchController;
  RxString searchQuery = ''.obs;

  // Loading state
  RxBool isSearching = false.obs;
  RxBool hasSearched = false.obs;

  // Search filters
  RxString selectedSearchType = 'all'.obs;
  List<String> searchTypes = ['all', 'title', 'type', 'author', 'publisher'];

  @override
  onInit() {
    searchController = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    searchController.dispose();
    super.onClose();
  }

  String getSearchTypeDisplayName(String type) {
    switch (type) {
      case 'all':
        return 'الكل';
      case 'title':
        return 'العنوان';
      case 'type':
        return 'النوع';
      case 'author':
        return 'المؤلف';
      case 'publisher':
        return 'دار النشر';
      default:
        return type;
    }
  }

  // Search Books from API
  searchBooks() async {
    if (searchController.text.trim().isEmpty) {
      Get.snackbar("تنبيه", "يرجى إدخال كلمة البحث");
      return;
    }

    try {
      isSearching.value = true;
      hasSearched.value = true;
      searchQuery.value = searchController.text.trim();

      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.searchBooks,
          method: RequestMethod.Get,
          params: {
            "query": searchQuery.value,
            "searchType": selectedSearchType.value,
            "limit": 50,
          },
        ),
      );

      if (response.success) {
        searchResults.value = response.data is List
            ? response.data
            : List<BookModel>.from((response.data['books'] ?? [])
                .map((x) => BookModel.fromJson(x)));
      } else {
        searchResults.value = [];
        Get.snackbar("خطأ", "فشل في البحث");
      }
    } catch (e) {
      searchResults.value = [];
      Get.snackbar("خطأ", "فشل في البحث");
    } finally {
      isSearching.value = false;
    }
  }

  // Advanced search with multiple criteria
  advancedSearch({
    String? title,
    String? type,
    String? author,
    String? publisher,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      isSearching.value = true;
      hasSearched.value = true;

      Map<String, dynamic> searchParams = {};

      if (title?.isNotEmpty == true) searchParams['title'] = title;
      if (type?.isNotEmpty == true) searchParams['type'] = type;
      if (author?.isNotEmpty == true) searchParams['author'] = author;
      if (publisher?.isNotEmpty == true) searchParams['publisher'] = publisher;
      if (minPrice != null) searchParams['minPrice'] = minPrice;
      if (maxPrice != null) searchParams['maxPrice'] = maxPrice;

      if (searchParams.isEmpty) {
        Get.snackbar("تنبيه", "يرجى إدخال معايير البحث");
        return;
      }

      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.advancedSearchBooks,
          method: RequestMethod.Post,
          body: searchParams,
        ),
      );

      if (response.success) {
        searchResults.value = List<Map<String, dynamic>>.from(
            response.data['books'] ?? response.data);
      } else {
        searchResults.clear();
        Get.snackbar("خطأ", "فشل في البحث المتقدم");
      }
    } catch (e) {
      searchResults.clear();
      Get.snackbar("خطأ", "فشل في البحث المتقدم");
    } finally {
      isSearching.value = false;
    }
  }

  // Clear search
  clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    searchResults.value = [];
    hasSearched.value = false;
    selectedSearchType.value = 'all';
  }

  // Quick search suggestions
  getSearchSuggestions() async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.getBookSuggestions,
          method: RequestMethod.Get,
        ),
      );

      if (response.success) {
        return List<String>.from(response.data['suggestions'] ?? []);
      }
    } catch (e) {
      // Handle error silently for suggestions
    }
    return <String>[];
  }
}
