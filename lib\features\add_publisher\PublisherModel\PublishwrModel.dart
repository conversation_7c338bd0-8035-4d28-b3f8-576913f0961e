import 'dart:convert';

class PublisherModel {
  int id;
  String pName;
  String city;
  List<dynamic>? books;

  PublisherModel({
    required this.id,
    required this.pName,
    required this.city,
    this.books,
  });

  factory PublisherModel.fromRawJson(String str) =>
      PublisherModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PublisherModel.fromJson(Map<String, dynamic> json) => PublisherModel(
        id: json["id"],
        pName: json["pName"],
        city: json["city"],
        books: List<dynamic>.from(json["books"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "pName": pName,
        "city": city,
        "books": List<dynamic>.from(books!.map((x) => x)),
      };
}
