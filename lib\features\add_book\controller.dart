import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/services/state_management/obs.dart';
import 'package:e_library/features/add_publisher/PublisherModel/PublishwrModel.dart';
import 'package:e_library/features/authors/model/AuthorModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../core/services/rest_api/rest_api.dart';

class AddBookController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Book fields: Id, Title, Type, Price, pubId, AuthorId
  late TextEditingController title, type, price;

  // Publisher and Author selection from API
  RxString selectedPublisherId = ''.obs;
  RxString selectedAuthorId = ''.obs;

  // Lists from API
  ObsList<PublisherModel> publishers = ObsList([]);
  ObsList<AuthorModel> Authors = ObsList([]);

  @override
  onInit() {
    title = TextEditingController();
    type = TextEditingController();
    price = TextEditingController();

    // Load data from API
    loadPublishers();
    loadAuthors();

    super.onInit();
  }

  @override
  onClose() {
    title.dispose();
    type.dispose();
    price.dispose();
    super.onClose();
  }

  // Load Publishers from API
  loadPublishers() async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
            endPoint: EndPoints.getPublishers,
            fromJson: PublisherModel.fromJson),
      );

      if (response.success) {
        publishers.value = response.data;
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل دور النشر");
    }
  }

  // Load Authors from API
  loadAuthors() async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(endPoint: EndPoints.getAuthors, fromJson: AuthorModel.fromJson),
      );

      if (response.success) {
        Authors.value = response.data;
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل المؤلفين");
    }
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedPublisherId.value.isEmpty) {
      Get.snackbar("خطأ", "يرجى اختيار دار النشر");
      return;
    }

    if (selectedAuthorId.value.isEmpty) {
      Get.snackbar("خطأ", "يرجى اختيار المؤلف");
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.addBook,
        method: RequestMethod.Post,
        body: {
          "title": title.text,
          "type": type.text,
          "price": double.tryParse(price.text) ?? 0.0,
          "publisherId": int.tryParse(selectedPublisherId.value) ?? 0,
          "authorId": int.tryParse(selectedAuthorId.value) ?? 0,
        },
      ),
    );

    if (response.success) {
      Get.snackbar(
        "نجح",
        "تم إضافة الكتاب بنجاح",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Clear form
      title.clear();
      type.clear();
      price.clear();
      selectedPublisherId.value = '';
      selectedAuthorId.value = '';
    } else {
      Get.snackbar(
        "خطأ",
        "فشل في إضافة الكتاب",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
