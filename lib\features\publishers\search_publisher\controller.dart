import 'package:e_library/core/config/app_builder.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class SearchPublisherController extends GetxController {
  AppBuilder appBuilder = Get.find();
  
  // Search results
  RxList<Map<String, dynamic>> searchResults = <Map<String, dynamic>>[].obs;
  
  // Search functionality
  late TextEditingController searchController;
  RxString searchQuery = ''.obs;
  
  // Loading state
  RxBool isSearching = false.obs;
  RxBool hasSearched = false.obs;
  
  // Search filters
  RxString selectedSearchType = 'all'.obs;
  List<String> searchTypes = ['all', 'pname', 'city'];

  @override
  onInit() {
    searchController = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    searchController.dispose();
    super.onClose();
  }

  String getSearchTypeDisplayName(String type) {
    switch (type) {
      case 'all': return 'الكل';
      case 'pname': return 'اسم دار النشر';
      case 'city': return 'المدينة';
      default: return type;
    }
  }

  // Search Publishers from API
  searchPublishers() async {
    if (searchController.text.trim().isEmpty) {
      Get.snackbar("تنبيه", "يرجى إدخال كلمة البحث");
      return;
    }

    try {
      isSearching.value = true;
      hasSearched.value = true;
      searchQuery.value = searchController.text.trim();
      
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.searchPublishers,
          method: RequestMethod.Get,
          params: {
            "query": searchQuery.value,
            "searchType": selectedSearchType.value,
            "limit": 50,
          },
        ),
      );
      
      if (response.success) {
        searchResults.value = List<Map<String, dynamic>>.from(response.data['publishers'] ?? response.data);
      } else {
        searchResults.clear();
        Get.snackbar("خطأ", "فشل في البحث");
      }
    } catch (e) {
      searchResults.clear();
      Get.snackbar("خطأ", "فشل في البحث");
    } finally {
      isSearching.value = false;
    }
  }

  // Advanced search with multiple criteria
  advancedSearch({
    String? pname,
    String? city,
  }) async {
    try {
      isSearching.value = true;
      hasSearched.value = true;
      
      Map<String, dynamic> searchParams = {};
      
      if (pname?.isNotEmpty == true) searchParams['pname'] = pname;
      if (city?.isNotEmpty == true) searchParams['city'] = city;
      
      if (searchParams.isEmpty) {
        Get.snackbar("تنبيه", "يرجى إدخال معايير البحث");
        return;
      }
      
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.advancedSearchPublishers,
          method: RequestMethod.Post,
          body: searchParams,
        ),
      );
      
      if (response.success) {
        searchResults.value = List<Map<String, dynamic>>.from(response.data['publishers'] ?? response.data);
      } else {
        searchResults.clear();
        Get.snackbar("خطأ", "فشل في البحث المتقدم");
      }
    } catch (e) {
      searchResults.clear();
      Get.snackbar("خطأ", "فشل في البحث المتقدم");
    } finally {
      isSearching.value = false;
    }
  }

  // Clear search
  clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    searchResults.clear();
    hasSearched.value = false;
    selectedSearchType.value = 'all';
  }

  // Get publisher's books
  getPublisherBooks(int publisherId) async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: "${EndPoints.getPublishers}/$publisherId/books",
          method: RequestMethod.Get,
        ),
      );
      
      if (response.success) {
        return List<Map<String, dynamic>>.from(response.data['books'] ?? []);
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل كتب دار النشر");
    }
    return <Map<String, dynamic>>[];
  }

  // Get search suggestions
  getSearchSuggestions() async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.getPublisherSuggestions,
          method: RequestMethod.Get,
        ),
      );
      
      if (response.success) {
        return List<String>.from(response.data['suggestions'] ?? []);
      }
    } catch (e) {
      // Handle error silently for suggestions
    }
    return <String>[];
  }

  // Get publisher statistics
  getPublisherStats(int publisherId) async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: "${EndPoints.getPublishers}/$publisherId/stats",
          method: RequestMethod.Get,
        ),
      );
      
      if (response.success) {
        return response.data;
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل إحصائيات دار النشر");
    }
    return null;
  }
}
