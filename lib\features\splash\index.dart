import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/splash/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(SplashScreenController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.lightPurple.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 120,
                width: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.auto_stories_rounded,
                  size: 80,
                  color: StyleRepo.darkPurple,
                ),
              ),
              const SizedBox(height: 50),
              Text(
                'E-Library',
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      color: StyleRepo.lavender,
                      fontWeight: FontWeight.bold,
                      fontSize: 28,
                    ),
              ),
              const SizedBox(height: 20),
              CircularProgressIndicator(
                color: StyleRepo.lavender,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
