import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/books/model/book_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AuthorBooksPage extends StatelessWidget {
  const AuthorBooksPage({super.key});

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.blue,
              StyleRepo.bluemedium,
              StyleRepo.darkPurple,
              StyleRepo.black.withValues(alpha: 0.9),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header with Author Info
              Container(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Navigation Row
                    Row(
                      children: [
                        IconButton(
                          onPressed: () => Get.back(),
                          icon: Icon(Icons.arrow_back, color: Colors.white, size: 28),
                        ),
                        SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            "كتب المؤلف",
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: controller.refreshBooks,
                          icon: Icon(Icons.refresh, color: Colors.white, size: 28),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),

                    // Author Info Card
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
                      ),
                      child: Row(
                        children: [
                          // Author Avatar
                          Container(
                            height: 70,
                            width: 70,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(35),
                            ),
                            child: Icon(
                              Icons.person,
                              color: StyleRepo.blue,
                              size: 35,
                            ),
                          ),
                          SizedBox(width: 15),
                          
                          // Author Details
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  controller.author.z,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                SizedBox(height: 5),
                                if (controller.author.country != null)
                                  Text(
                                    '${controller.author.country}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white70,
                                    ),
                                  ),
                                if (controller.author.city != null)
                                  Text(
                                    '${controller.author.city}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white70,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          
                          // Books Count
                          Obx(() => Container(
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              '${controller.authorBooks.length} كتاب',
                              style: TextStyle(
                                color: StyleRepo.blue,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          )),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Search Bar
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 8,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: controller.searchController,
                    decoration: InputDecoration(
                      hintText: "البحث في كتب المؤلف...",
                      prefixIcon: Icon(Icons.search, color: StyleRepo.blue),
                      suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                          ? IconButton(
                              onPressed: controller.clearSearch,
                              icon: Icon(Icons.clear, color: StyleRepo.blue),
                            )
                          : SizedBox()),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide(color: StyleRepo.blue, width: 2),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 20),

              // Books List
              Expanded(
                child: Obx(() {
                  if (controller.isLoading.value && controller.authorBooks.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.white),
                          SizedBox(height: 20),
                          Text(
                            "جاري تحميل كتب المؤلف...",
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.filteredBooks.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.book_outlined,
                            size: 80,
                            color: Colors.white54,
                          ),
                          SizedBox(height: 20),
                          Text(
                            controller.searchQuery.value.isNotEmpty
                                ? "لا توجد نتائج للبحث"
                                : "لا توجد كتب لهذا المؤلف",
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white70,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            controller.searchQuery.value.isNotEmpty
                                ? 'لم يتم العثور على كتب تحتوي على "${controller.searchQuery.value}"'
                                : "لم يتم نشر أي كتب لهذا المؤلف بعد",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white54,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async => await controller.refreshBooks(),
                    child: ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      itemCount: controller.filteredBooks.length + 
                                 (controller.hasMoreData.value ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == controller.filteredBooks.length) {
                          // Load more indicator
                          return Padding(
                            padding: EdgeInsets.all(20),
                            child: Center(
                              child: controller.isLoading.value
                                  ? CircularProgressIndicator(color: Colors.white)
                                  : ElevatedButton(
                                      onPressed: controller.loadMoreBooks,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: StyleRepo.blue,
                                        foregroundColor: Colors.white,
                                      ),
                                      child: Text("تحميل المزيد"),
                                    ),
                            ),
                          );
                        }

                        final book = controller.filteredBooks[index];
                        return _buildBookCard(book);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookCard(BookModel book) {
    return Container(
      margin: EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(15),
        child: Row(
          children: [
            // Book Icon
            Container(
              height: 60,
              width: 50,
              decoration: BoxDecoration(
                color: StyleRepo.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.menu_book,
                color: StyleRepo.blue,
                size: 30,
              ),
            ),
            SizedBox(width: 15),
            
            // Book Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    book.title ?? 'عنوان غير محدد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: StyleRepo.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 5),
                  Text(
                    'النوع: ${book.type ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (book.publisherName != null)
                    Text(
                      'دار النشر: ${book.publisherName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
            
            // Price
            if (book.price != null)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: StyleRepo.blue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${book.price} ر.س',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
