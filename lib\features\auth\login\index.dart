import 'package:e_library/core/routes/routes.dart';
import 'package:e_library/core/style/repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LoginPageController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.darkPurple,
              // StyleRepo.deepPurple,
              //    StyleRepo.purple,
              StyleRepo.lightPurple.withValues(alpha: 0.8),
            ],
            //   stops: [0.0, 0.3, 0.7],
          ),
        ),
        child: Form(
          key: controller.formKey,
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 30),
            children: [
              SizedBox(height: MediaQuery.sizeOf(context).height * .12),
              // Logo
              Center(
                child: Container(
                  height: 100,
                  width: 100,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.auto_stories_rounded,
                      size: 65,
                      color: StyleRepo.deepPurple,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 35),
              // Title
              Center(
                child: Text(
                  "Login",
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        blurRadius: 5,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 50),
              TextFormField(
                decoration: InputDecoration(
                  hintText: "Email",
                  prefixIcon:
                      Icon(Icons.email_outlined, color: StyleRepo.deepPurple),
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return "This field is required";
                  }
                  if (!value.contains("@")) {
                    return "wrong Email";
                  }
                  return null;
                },
              ),
              SizedBox(height: 25),

              TextFormField(
                controller: controller.password,
                obscureText: true,
                decoration: InputDecoration(
                  hintText: "password",
                  prefixIcon:
                      Icon(Icons.lock_outline, color: StyleRepo.deepPurple),
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return "This field is required";
                  }
                  return null;
                },
              ),
              SizedBox(height: 25),

              ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  "تسجيل الدخول",
                ),
              ),
              SizedBox(height: 25),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "ليس لديك حساب؟",
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Colors.white,
                        ),
                  ),
                  TextButton(
                    onPressed: () => Get.toNamed(Pages.register.value),
                    child: Text(
                      "إنشاء حساب جديد",
                      
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Colors.white,
                        color: Colors.amberAccent,
                     
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
