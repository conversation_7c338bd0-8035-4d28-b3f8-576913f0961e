import 'package:e_library/core/routes/routes.dart';
import 'package:e_library/core/style/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'core/config/app_builder.dart';

void main() async {
  runApp(
    const MyApp(),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    Get.put(AppBuilder());

    return GetMaterialApp(
      title: 'Flutter Demo',
      theme: AppStyle.theme,

      //
      initialRoute: '/',
      unknownRoute: AppRouting.unknownRoute,
      getPages: AppRouting.routes,
    );
  }
}
