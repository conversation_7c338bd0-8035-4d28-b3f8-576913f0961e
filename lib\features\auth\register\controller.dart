import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/config/role.dart';
import 'package:e_library/core/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  AppBuilder appBuilder = Get.find();

  late TextEditingController username, password, firstName, lastName;

  RxBool selectedUserType = false.obs;
  List<bool> userTypes = [true, false];
  @override
  onInit() {
    username = TextEditingController();
    password = TextEditingController();
    firstName = TextEditingController();
    lastName = TextEditingController();

    super.onInit();
  }

  @override
  onClose() {
    username.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        body: {
          "username": username.text,
          "password": password.text,
          "fName": firstName.text,
          "lName": lastName.text,
          "isAdmin": selectedUserType.value
        },
      ),
    );

    if (response.success) {
      Role selectedRole =
          selectedUserType.value == 'admin' ? Role.admin : Role.user;
      appBuilder.setRole(selectedRole);
      Get.toNamed(Pages.home.value);
    } else {}
  }
}
