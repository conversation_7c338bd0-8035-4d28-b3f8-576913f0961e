import 'dart:convert';

class AuthorModel {
  int id;
  String fName;
  String lName;
  String country;
  String city;
  String address;
  List<dynamic>? books;

  AuthorModel({
    required this.id,
    required this.fName,
    required this.lName,
    required this.country,
    required this.city,
    required this.address,
    this.books,
  });

  factory AuthorModel.fromRawJson(String str) =>
      AuthorModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AuthorModel.fromJson(Map<String, dynamic> json) => AuthorModel(
        id: json["id"],
        fName: json["fName"],
        lName: json["lName"],
        country: json["country"],
        city: json["city"],
        address: json["address"],
        books: List<dynamic>.from(json["books"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "fName": fName,
        "lName": lName,
        "country": country,
        "city": city,
        "address": address,
        "books": List<dynamic>.from(books!.map((x) => x)),
      };
}
