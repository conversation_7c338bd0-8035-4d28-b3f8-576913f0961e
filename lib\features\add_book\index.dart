import 'package:e_library/core/services/state_management/widgets/obs_widget.dart';
import 'package:e_library/core/style/repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class AddBookPage extends StatelessWidget {
  const AddBookPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddBookController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.deepPurple,
              StyleRepo.blue,
              StyleRepo.black.withValues(alpha: 0.8),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Form(
            key: controller.formKey,
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 30),
              children: [
                SizedBox(height: 20),
                // Header
                Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon:
                          Icon(Icons.arrow_back, color: Colors.white, size: 28),
                    ),
                    SizedBox(width: 10),
                    Text(
                      "إضافة كتاب جديد",
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 30),

                // Book Icon
                Center(
                  child: Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Icon(
                        Icons.menu_book_rounded,
                        size: 45,
                        color: StyleRepo.deepPurple,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 30),

                // Title Field
                _buildTextField(
                  controller: controller.title,
                  hintText: "عنوان الكتاب",
                  icon: Icons.title,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // Type Field
                _buildTextField(
                  controller: controller.type,
                  hintText: "نوع الكتاب",
                  icon: Icons.category,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // Price Field
                _buildTextField(
                  controller: controller.price,
                  hintText: "السعر",
                  icon: Icons.attach_money,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return "هذا الحقل مطلوب";
                    }
                    if (double.tryParse(value) == null) {
                      return "يرجى إدخال رقم صحيح";
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),

                // Publisher Dropdown from API
                _buildPublisherDropdown(controller),
                SizedBox(height: 20),

                // Author Dropdown from API
                _buildAuthorDropdown(controller),
                SizedBox(height: 40),

                // Add Button
                Container(
                  height: 55,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: StyleRepo.purple.withValues(alpha: 0.4),
                        blurRadius: 10,
                        offset: Offset(0, 5),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: controller.confirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: StyleRepo.deepPurple,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      "إضافة الكتاب",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        decoration: InputDecoration(
          hintText: hintText,
          prefixIcon: Icon(icon, color: StyleRepo.deepPurple),
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide(color: StyleRepo.deepPurple, width: 2),
          ),
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildDropdown({
    required RxString value,
    required List<String> items,
    required String hintText,
    required IconData icon,
    required String Function(String) getDisplayName,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Obx(() => DropdownButtonFormField<String>(
            value: value.value,
            decoration: InputDecoration(
              hintText: hintText,
              prefixIcon: Icon(icon, color: StyleRepo.deepPurple),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide(color: StyleRepo.deepPurple, width: 2),
              ),
            ),
            items: items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(
                  getDisplayName(item),
                  style: TextStyle(
                    color: StyleRepo.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              if (newValue != null) {
                value.value = newValue;
              }
            },
            dropdownColor: Colors.white,
            icon: Icon(
              Icons.arrow_drop_down,
              color: StyleRepo.deepPurple,
            ),
          )),
    );
  }

  // Publisher Dropdown from API
  Widget _buildPublisherDropdown(AddBookController controller) {
    return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 8,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: ObsListBuilder(
          obs: controller.publishers,
          builder: (context, value) {
            return DropdownButtonFormField<String>(
              value: controller.selectedPublisherId.value.isEmpty
                  ? null
                  : controller.selectedPublisherId.value,
              decoration: InputDecoration(
                hintText: "اختر دار النشر",
                prefixIcon: Icon(Icons.business, color: StyleRepo.deepPurple),
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide(color: StyleRepo.deepPurple, width: 2),
                ),
              ),
              items: controller.publishers.map((publisher) {
                return DropdownMenuItem<String>(
                  value: publisher.id.toString(),
                  child: Text(
                    publisher.pName ?? 'غير محدد',
                    style: TextStyle(
                      color: StyleRepo.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  controller.selectedPublisherId.value = newValue;
                }
              },
              dropdownColor: Colors.white,
              icon: Icon(
                Icons.arrow_drop_down,
                color: StyleRepo.deepPurple,
              ),
            );
          },
        ));
  }

  // Author Dropdown from API
  Widget _buildAuthorDropdown(AddBookController controller) {
    return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 8,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: ObsListBuilder(
            obs: controller.Authors,
            builder: (context, Authors) {
              return DropdownButtonFormField<String>(
                value: controller.selectedAuthorId.value.isEmpty
                    ? null
                    : controller.selectedAuthorId.value,
                decoration: InputDecoration(
                  hintText: "اختر المؤلف",
                  prefixIcon: Icon(Icons.person, color: StyleRepo.deepPurple),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide:
                        BorderSide(color: StyleRepo.deepPurple, width: 2),
                  ),
                ),
                items: controller.Authors.map((author) {
                  return DropdownMenuItem<String>(
                    value: author.id.toString(),
                    child: Text(
                      '${author.fName} ${author.lName}',
                      style: TextStyle(
                        color: StyleRepo.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    controller.selectedAuthorId.value = newValue;
                  }
                },
                dropdownColor: Colors.white,
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: StyleRepo.deepPurple,
                ),
              );
            }));
  }
}
