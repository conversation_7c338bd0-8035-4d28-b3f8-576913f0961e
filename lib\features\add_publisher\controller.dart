import 'package:e_library/core/config/app_builder.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/services/rest_api/rest_api.dart';

class AddPublisherController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Publisher fields: Id, PName, City
  late TextEditingController pname, city;

  @override
  onInit() {
    pname = TextEditingController();
    city = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    pname.dispose();
    city.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.addPublisher,
        method: RequestMethod.Post,
        params: {"success": true},
        body: {
          "pname": pname.text,
          "city": city.text,
        },
      ),
    );

    if (response.success) {
      Get.snackbar(
        "نجح",
        "تم إضافة دار النشر بنجاح",
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Clear form
      pname.clear();
      city.clear();
    } else {
      Get.snackbar(
        "خطأ",
        "فشل في إضافة دار النشر",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
