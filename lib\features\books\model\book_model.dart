class BookModel {
  int? id;
  String? title;
  String? type;
  double? price;
  int? pubId;
  int? authorId;
  String? authorName;
  String? publisherName;

  BookModel({
    this.id,
    this.title,
    this.type,
    this.price,
    this.pubId,
    this.authorId,
    this.authorName,
    this.publisherName,
  });

  BookModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    type = json['type'];
    price = json['price']?.toDouble();
    pubId = json['pubId'] ?? json['publisherId'];
    authorId = json['authorId'];
    authorName = json['authorName'] ?? json['author_name'];
    publisherName =
        json['publisherName'] ?? json['publisher_name'] ?? json['pname'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['type'] = type;
    data['price'] = price;
    data['pubId'] = pubId;
    data['authorId'] = authorId;
    data['authorName'] = authorName;
    data['publisherName'] = publisherName;
    return data;
  }

  @override
  String toString() {
    return 'BookModel{id: $id, title: $title, type: $type, price: $price, pubId: $pubId, authorId: $authorId, authorName: $authorName, publisherName: $publisherName}';
  }
}
