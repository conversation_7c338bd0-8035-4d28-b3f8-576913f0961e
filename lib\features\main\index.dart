import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/home/<USER>';
import 'package:e_library/features/main/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'widgets/nav_bar.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MainPageController());
    return Scaffold(
      bottomNavigationBar: NavBar(),
      body: Obx(
        () => switch (controller.currentPage.value) {
          0 => HomePage(),
          _ => ColoredBox(color: StyleRepo.royalPurple),
        },
      ),
    );
  }
}
