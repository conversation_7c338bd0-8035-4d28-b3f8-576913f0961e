// import 'package:e_library/core/localization/strings.dart';
// import 'package:e_library/core/widgets/svg_icon.dart';
// import 'package:e_library/gen/assets.gen.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import '../controller.dart';

// class NavBar extends StatelessWidget {
//   const NavBar({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.find<MainPageController>();
//     // MainPageController controller = Get.find();
//     return Obx(
//       () => NavigationBar(
//         onDestinationSelected: (page) => controller.currentPage.value = page,
//         selectedIndex: controller.currentPage.value,
//         destinations: [
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.home),
//             // icon: Icon(Icons.home),
//             label: "home",
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.explore),
//             label: "home",
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.cart),
//             label: "home",
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.heart),
//             label: "home",
//           ),
//           NavigationDestination(
//             icon: SvgIcon(icon: Assets.icons.profile),
//             label: "home",
//           ),
//         ],
//       ),
//     );
//   }
// }
