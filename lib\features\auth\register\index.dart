import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/auth/register/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RegisterPageController());
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.lightPurple.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Form(
          key: controller.formKey,
          child: ListView(
            padding: EdgeInsets.all(15),
            children: [
              SizedBox(height: MediaQuery.sizeOf(context).height * .20),
              Center(
                child: Text(
                  "Create New Account",
                  style: Theme.of(context)
                      .textTheme
                      .headlineSmall!
                      .copyWith(color: Colors.white, fontSize: 28),
                ),
              ),
              Si<PERSON><PERSON><PERSON>(height: 40),
              TextFormField(
                controller: controller.firstName,
                decoration: InputDecoration(
                  hintText: "First Name",
                  prefixIcon:
                      Icon(Icons.person_outline, color: StyleRepo.deepPurple),
                ),
                validator: (value) =>
                    value!.isEmpty ? "Name is required" : null,
              ),
              SizedBox(height: 20),
              TextFormField(
                controller: controller.lastName,
                decoration: InputDecoration(
                  hintText: "Last Name",
                  prefixIcon:
                      Icon(Icons.person_outline, color: StyleRepo.deepPurple),
                ),
                validator: (value) =>
                    value!.isEmpty ? "Name is required" : null,
              ),
              SizedBox(height: 20),
              TextFormField(
                controller: controller.username,
                decoration: InputDecoration(
                  hintText: "Email",
                  prefixIcon:
                      Icon(Icons.email_outlined, color: StyleRepo.deepPurple),
                ),
                validator: (value) {
                  if (value!.isEmpty) return "Email is required";
                  if (!value.contains("@")) return "Invalid email";
                  return null;
                },
              ),
              SizedBox(height: 20),
              TextFormField(
                controller: controller.password,
                obscureText: true,
                decoration: InputDecoration(
                  hintText: "Password",
                  prefixIcon:
                      Icon(Icons.lock_outline, color: StyleRepo.deepPurple),
                ),
                validator: (value) {
                  if (value!.isEmpty) return "Password is required";
                  if (value.length < 8)
                    return "Password must be at least 8 characters";
                  return null;
                },
              ),
              SizedBox(height: 20),
              TextFormField(
                obscureText: true,
                decoration: InputDecoration(
                  hintText: "Confirm Password",
                  prefixIcon:
                      Icon(Icons.lock_outline, color: StyleRepo.deepPurple),
                ),
                validator: (value) {
                  if (value!.isEmpty) return "Please confirm your password";
                  if (value != controller.password.text)
                    return "Passwords do not match";
                  return null;
                },
              ),
              SizedBox(height: 20),
              Obx(() => DropdownButtonFormField<bool>(
                    value: controller.selectedUserType.value,
                    decoration: InputDecoration(
                      hintText: "Type User",
                      prefixIcon: Icon(Icons.person_outline,
                          color: StyleRepo.deepPurple),
                    ),
                    items: controller.userTypes.map((bool type) {
                      return DropdownMenuItem<bool>(
                        value: type,
                        child: Text(
                          type == true ? "Admin" : "User",
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      );
                    }).toList(),
                    onChanged: (bool? newValue) {
                      if (newValue != null) {
                        controller.selectedUserType.value = newValue;
                      }
                    },
                    dropdownColor: StyleRepo.white,
                    icon: Icon(
                      Icons.arrow_drop_down,
                      color: StyleRepo.deepPurple,
                    ),
                  )),
              SizedBox(height: 20),
              ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  "Sign Up",
                ),
              ),
              SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Already have an account?",
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Colors.white,
                        ),
                  ),
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text(
                      "Login",
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: StyleRepo.lavender,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
