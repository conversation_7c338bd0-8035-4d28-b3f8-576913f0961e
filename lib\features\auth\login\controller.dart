import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/config/role.dart';
import 'package:e_library/core/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late TextEditingController email, password;

  // User type selection
  RxString selectedUserType = 'user'.obs;
  List<String> userTypes = ['user', 'admin'];

  @override
  onInit() {
    email = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    email.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.login,
        method: RequestMethod.Post,
        params: {"success": true},
        body: {
          "email": email.text,
          "password": password.text,
        },
      ),
    );
    if (response.success) {
      // Set role based on selected user type
      Role selectedRole =
          selectedUserType.value == 'admin' ? Role.admin : Role.user;
      appBuilder.setRole(selectedRole);
      appBuilder.setToken(response.data['access_token']);

      Get.toNamed(Pages.home.value);
    } else {}
  }
}
