import 'package:flutter/material.dart';

class StyleRepo {
  static const transparent = Colors.transparent;
  static const white = Color(0xFFFCFCFC);
  static const lightGrey = Color(0xFFB1B1B1);
  static const darkGrey = Color(0xFF7C7C7C);
  static const mediumGrey = Color(0xFFF3F3F3);
  static const black = Color(0xFF030303);
  static const blue = Color(0xFF003399);
  static const gray = Color(0xFFA4A4A4);
  static const bluemedium = Color.fromARGB(255, 0, 54, 161);
  static const blueGray = Color(0xFF57597E);
  static const darkred = Color(0xFFB51A1E);
  static const meduimred = Color(0xFF992534);
  static const yellow = Colors.amber;
  static const whiteblue = Color(0xFF0048D9);
  static const whiteSmoke = Color(0xFFFAFAFA);

  // Purple gradient colors
  static const deepPurple = Color(0xFF673AB7);
  static const purple = Color(0xFF9C27B0);
  static const lightPurple = Color(0xFFBA68C8);
  static const purpleAccent = Color(0xFFE1BEE7);
  static const darkPurple = Color(0xFF4A148C);
  static const mediumPurple = Color(0xFF7B1FA2);
  static const softPurple = Color(0xFFCE93D8);
  static const vibrantPurple = Color(0xFF8E24AA);
  static const royalPurple = Color(0xFF6A1B9A);
  static const lavender = Color(0xFFE8EAF6);
  static const plum = Color(0xFF8E24AA);
  static const orchid = Color(0xFFAB47BC);
}
