import 'package:e_library/core/routes/routes.dart';
import 'package:e_library/core/style/repo.dart';
import 'package:e_library/features/authors/model/AuthorModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AuthorCard extends StatelessWidget {
  final AuthorModel author;
  const AuthorCard({super.key, required this.author});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // Navigate to AuthorBooksPage with author data
        Get.toNamed(Pages.authorBooks.value);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 15),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 8,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(15),
          child: Row(
            children: [
              // Author Icon
              Container(
                height: 60,
                width: 60,
                decoration: BoxDecoration(
                  color: StyleRepo.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.person,
                  color: StyleRepo.blue,
                  size: 30,
                ),
              ),
              SizedBox(width: 15),

              // Author Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      author.fName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: StyleRepo.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 5),
                    Text(
                      'البلد: ${author.country}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      'المدينة: ${author.city}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      'العنوان: ${author.address}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
