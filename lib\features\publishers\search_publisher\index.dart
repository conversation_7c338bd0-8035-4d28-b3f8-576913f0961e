import 'package:e_library/core/style/repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class SearchPublisherPage extends StatelessWidget {
  const SearchPublisherPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SearchPublisherController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.black,
              StyleRepo.darkGrey,
              StyleRepo.blueGray,
              StyleRepo.darkPurple.withValues(alpha: 0.8),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.arrow_back, color: Colors.white, size: 28),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        "البحث في دور النشر",
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: controller.clearSearch,
                      icon: Icon(Icons.clear_all, color: Colors.white, size: 28),
                    ),
                  ],
                ),
              ),

              // Search Section
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    // Search Type Dropdown
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 8,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Obx(() => DropdownButtonFormField<String>(
                        value: controller.selectedSearchType.value,
                        decoration: InputDecoration(
                          hintText: "نوع البحث",
                          prefixIcon: Icon(Icons.filter_list, color: StyleRepo.black),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(color: StyleRepo.black, width: 2),
                          ),
                        ),
                        items: controller.searchTypes.map((String type) {
                          return DropdownMenuItem<String>(
                            value: type,
                            child: Text(
                              controller.getSearchTypeDisplayName(type),
                              style: TextStyle(
                                color: StyleRepo.black,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            controller.selectedSearchType.value = newValue;
                          }
                        },
                        dropdownColor: Colors.white,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: StyleRepo.black,
                        ),
                      )),
                    ),
                    SizedBox(height: 15),

                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 8,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: controller.searchController,
                        onSubmitted: (_) => controller.searchPublishers(),
                        decoration: InputDecoration(
                          hintText: "ابحث عن دور النشر...",
                          prefixIcon: Icon(Icons.search, color: StyleRepo.black),
                          suffixIcon: IconButton(
                            onPressed: controller.searchPublishers,
                            icon: Icon(Icons.send, color: StyleRepo.black),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(color: StyleRepo.black, width: 2),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 15),

                    // Search Button
                    Container(
                      width: double.infinity,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: StyleRepo.darkGrey.withValues(alpha: 0.4),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: controller.searchPublishers,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: StyleRepo.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          elevation: 0,
                        ),
                        child: Obx(() => controller.isSearching.value
                            ? SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                "بحث",
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              )),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),

              // Search Results
              Expanded(
                child: Obx(() {
                  if (!controller.hasSearched.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.business_center,
                            size: 80,
                            color: Colors.white54,
                          ),
                          SizedBox(height: 20),
                          Text(
                            "ابحث عن دور النشر",
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white70,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            "استخدم شريط البحث أعلاه للعثور على دور النشر",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white54,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.isSearching.value) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.white),
                          SizedBox(height: 20),
                          Text(
                            "جاري البحث...",
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (controller.searchResults.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.business_off,
                            size: 80,
                            color: Colors.white54,
                          ),
                          SizedBox(height: 20),
                          Text(
                            "لا توجد نتائج",
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white70,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'لم يتم العثور على دور نشر تحتوي على "${controller.searchQuery.value}"',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white54,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    itemCount: controller.searchResults.length,
                    itemBuilder: (context, index) {
                      final publisher = controller.searchResults[index];
                      return _buildPublisherCard(publisher);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPublisherCard(Map<String, dynamic> publisher) {
    return Container(
      margin: EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(15),
        child: Row(
          children: [
            // Publisher Icon
            Container(
              height: 60,
              width: 60,
              decoration: BoxDecoration(
                color: StyleRepo.black.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.business,
                color: StyleRepo.black,
                size: 30,
              ),
            ),
            SizedBox(width: 15),
            
            // Publisher Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    publisher['pname'] ?? 'اسم غير محدد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: StyleRepo.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 5),
                  if (publisher['city'] != null)
                    Text(
                      'المدينة: ${publisher['city']}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  if (publisher['id'] != null)
                    Text(
                      'المعرف: ${publisher['id']}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                ],
              ),
            ),
            
            // Books Count (if available)
            if (publisher['booksCount'] != null)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: StyleRepo.black,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${publisher['booksCount']} كتاب',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
