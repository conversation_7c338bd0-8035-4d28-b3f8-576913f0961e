import 'package:e_library/core/config/app_builder.dart';
import 'package:e_library/core/services/state_management/obs.dart';
import 'package:e_library/features/authors/model/AuthorModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class SearchAuthorController extends GetxController {
  AppBuilder appBuilder = Get.find();

  // Search results
  ObsList<AuthorModel> searchResults = ObsList([]);

  // Search functionality
  late TextEditingController searchController;

  // Loading state

  @override
  onInit() {
    searchController = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Search Authors from API
  searchAuthors() async {
    if (searchController.text.trim().isEmpty) {
      Get.snackbar("تنبيه", "يرجى إدخال كلمة البحث");
      clearSearch();
      return;
    }

    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.searchAuthors,
          fromJson: AuthorModel.fromJson,
          params: {
            "name": searchController.text.trim(),
          },
        ),
      );

      if (response.success) {
        searchResults.value = response.data;
      } else {
        searchResults.value = [];
        Get.snackbar("خطأ", "فشل في البحث");
      }
    } catch (e) {
      searchResults.value = [];
      Get.snackbar("خطأ", "فشل في البحث");
    }
  }

  // Advanced search with multiple criteria

  // Clear search
  clearSearch() {
    searchController.clear();
    searchResults.value = [];
  }

  // Get author's books
  getAuthorBooks(int authorId) async {
    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: "${EndPoints.getAuthors}/$authorId/books",
          method: RequestMethod.Get,
        ),
      );

      if (response.success) {
        return List<Map<String, dynamic>>.from(response.data['books'] ?? []);
      }
    } catch (e) {
      Get.snackbar("خطأ", "فشل في تحميل كتب المؤلف");
    }
    return <Map<String, dynamic>>[];
  }

  // Get search suggestions
}
