import 'package:e_library/core/style/repo.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controller.dart';

class ViewBooksPage extends StatelessWidget {
  const ViewBooksPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ViewBooksController());

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              StyleRepo.darkPurple,
              StyleRepo.deepPurple,
              StyleRepo.blue,
              StyleRepo.black.withValues(alpha: 0.8),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon:
                          Icon(Icons.arrow_back, color: Colors.white, size: 28),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        "جميع الكتب",
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: controller.refreshBooks,
                      icon: Icon(Icons.refresh, color: Colors.white, size: 28),
                    ),
                  ],
                ),
              ),

              // Search Bar
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 8,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: controller.searchController,
                    decoration: InputDecoration(
                      hintText: "البحث في الكتب...",
                      prefixIcon:
                          Icon(Icons.search, color: StyleRepo.deepPurple),
                      suffixIcon:
                          Obx(() => controller.searchQuery.value.isNotEmpty
                              ? IconButton(
                                  onPressed: controller.clearSearch,
                                  icon: Icon(Icons.clear,
                                      color: StyleRepo.deepPurple),
                                )
                              : SizedBox()),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide:
                            BorderSide(color: StyleRepo.deepPurple, width: 2),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 20),

              // Books List
              Expanded(
                child: Obx(() {
                  if (controller.isLoading.value && controller.books.isEmpty) {
                    return Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  if (controller.filteredBooks.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.book_outlined,
                            size: 80,
                            color: Colors.white54,
                          ),
                          SizedBox(height: 20),
                          Text(
                            controller.searchQuery.value.isNotEmpty
                                ? "لا توجد نتائج للبحث"
                                : "لا توجد كتب",
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async => await controller.refreshBooks(),
                    child: ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      itemCount: controller.filteredBooks.length +
                          (controller.hasMoreData.value ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index == controller.filteredBooks.length) {
                          // Load more indicator
                          return Padding(
                            padding: EdgeInsets.all(20),
                            child: Center(
                              child: controller.isLoading.value
                                  ? CircularProgressIndicator(
                                      color: Colors.white)
                                  : ElevatedButton(
                                      onPressed: controller.loadMoreBooks,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: StyleRepo.deepPurple,
                                        foregroundColor: Colors.white,
                                      ),
                                      child: Text("تحميل المزيد"),
                                    ),
                            ),
                          );
                        }

                        final book = controller.filteredBooks[index];
                        return _buildBookCard(book);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookCard(Map<String, dynamic> book) {
    return Container(
      margin: EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(15),
        child: Row(
          children: [
            // Book Icon
            Container(
              height: 60,
              width: 50,
              decoration: BoxDecoration(
                color: StyleRepo.deepPurple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.menu_book,
                color: StyleRepo.deepPurple,
                size: 30,
              ),
            ),
            SizedBox(width: 15),

            // Book Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    book['title'] ?? 'عنوان غير محدد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: StyleRepo.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 5),
                  Text(
                    'النوع: ${book['type'] ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 3),
                  Text(
                    'المؤلف: ${book['authorName'] ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 3),
                  Text(
                    'دار النشر: ${book['publisherName'] ?? 'غير محدد'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // Price
            if (book['price'] != null)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: StyleRepo.deepPurple,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${book['price']} ر.س',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
