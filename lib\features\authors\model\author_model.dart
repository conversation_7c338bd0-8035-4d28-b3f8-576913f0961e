class AuthorModel {
  int? id;
  String? fName;
  String? lName;
  String? country;
  String? city;
  String? address;
  int? booksCount;

  AuthorModel({
    this.id,
    this.fName,
    this.lName,
    this.country,
    this.city,
    this.address,
    this.booksCount,
  });

  AuthorModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    fName = json['fName'] ?? json['fname'] ?? json['first_name'];
    lName = json['lName'] ?? json['lname'] ?? json['last_name'];
    country = json['country'];
    city = json['city'];
    address = json['address'];
    booksCount = json['booksCount'] ?? json['books_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['fName'] = fName;
    data['lName'] = lName;
    data['country'] = country;
    data['city'] = city;
    data['address'] = address;
    data['booksCount'] = booksCount;
    return data;
  }

  String get fullName => '${fName ?? ''} ${lName ?? ''}'.trim();

  @override
  String toString() {
    return 'AuthorModel{id: $id, fName: $fName, lName: $lName, country: $country, city: $city, address: $address, booksCount: $booksCount}';
  }
}
