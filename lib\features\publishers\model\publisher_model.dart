class PublisherModel {
  int? id;
  String? pName;
  String? city;
  int? booksCount;

  PublisherModel({
    this.id,
    this.pName,
    this.city,
    this.booksCount,
  });

  PublisherModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    pName = json['pname'] ?? json['pName'] ?? json['name'];
    city = json['city'];
    booksCount = json['booksCount'] ?? json['books_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['pname'] = pName;
    data['city'] = city;
    data['booksCount'] = booksCount;
    return data;
  }

  @override
  String toString() {
    return 'PublisherModel{id: $id, pName: $pName, city: $city, booksCount: $booksCount}';
  }
}
